import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, FlatList, TouchableOpacity, Image } from "react-native";
import { useLocalSearchParams } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { getAllShowcases, getShowcasesByType, type Showcase } from "../../../data/education/showcases";

export default function ShowcasesScreen() {
  const params = useLocalSearchParams();
  const [showcases, setShowcases] = useState<Showcase[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Get filter from URL params
  const currentFilter = (params.filter as string) || "all";

  useEffect(() => {
    loadShowcases();
  }, [currentFilter]);

  const loadShowcases = async () => {
    try {
      setLoading(true);
      let showcasesData: Showcase[];
      
      if (currentFilter === "all") {
        showcasesData = await getAllShowcases();
      } else {
        showcasesData = await getShowcasesByType(currentFilter as "upcoming" | "registered" | "completed");
      }
      
      setShowcases(showcasesData);
    } catch (error) {
      console.error('Error loading showcases:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderShowcaseCard = ({ item }: { item: Showcase }) => (
    <TouchableOpacity style={styles.showcaseCard} activeOpacity={0.7}>
      <Image source={{ uri: item.image }} style={styles.showcaseImage} />
      <View style={styles.showcaseContent}>
        <View style={styles.showcaseHeader}>
          <Text style={styles.showcaseName}>{item.name}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        </View>
        <Text style={styles.showcaseDetails}>{item.duration} • {item.ageGroup}</Text>
        <Text style={styles.showcaseLocation}>
          <MaterialIcons name="location-on" size={16} color="#666" />
          {item.location}
        </Text>
        <Text style={styles.showcaseDate}>{item.date}</Text>
        <View style={styles.showcaseFooter}>
          <Text style={styles.showcasePrice}>{item.price}</Text>
          <Text style={styles.participantCount}>
            {item.participants}/{item.maxParticipants} registered
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Open": return "#28a745";
      case "Almost Full": return "#ffc107";
      case "Full": return "#dc3545";
      case "Completed": return "#6c757d";
      default: return "#17a2b8";
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Showcases</Text>
        <Text style={styles.subtitle}>
          Talent showcase events and competitions
        </Text>
      </View>

      <View style={styles.content}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text>Loading showcases...</Text>
          </View>
        ) : showcases.length > 0 ? (
          <FlatList
            data={showcases}
            renderItem={renderShowcaseCard}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <MaterialIcons name="event-available" size={48} color="#8E8E93" />
            <Text style={styles.emptyText}>No showcases available</Text>
            <Text style={styles.emptySubtext}>
              Check back soon for upcoming talent showcase events
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  showcaseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  showcaseImage: {
    width: '100%',
    height: 200,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  showcaseContent: {
    padding: 16,
  },
  showcaseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  showcaseName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1C1C1E',
    flex: 1,
    marginRight: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  showcaseDetails: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 8,
  },
  showcaseLocation: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  showcaseDate: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
    marginBottom: 12,
  },
  showcaseFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
  },
  showcasePrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#34C759',
  },
  participantCount: {
    fontSize: 14,
    color: '#8E8E93',
  },
  placeholder: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
  },
});
