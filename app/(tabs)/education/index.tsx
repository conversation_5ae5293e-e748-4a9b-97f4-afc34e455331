import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, FlatList, TouchableOpacity, Alert } from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import Header from "./components/Header";
import EducationTabs from "./components/EducationTabs";
import EducationCard from "./components/EducationCard";
import AddEducationModal from "./components/AddEducationModal";
import {
  getEducationItems,
  educationData,
  EducationItem,
  EducationDetails,
  deleteEducationItem,
} from "./../../../data/education/education";

export const navigationOptions = {
  headerShown: false,
};

function HockeyCampsContent({
  educationData,
  onEdit,
  onRemove,
}: {
  educationData: EducationItem[];
  onEdit: (item: EducationItem) => void;
  onRemove: (item: EducationItem) => void;
}) {
  const router = useRouter();
  const campData = educationData.filter((item) => item.type === "camp");

  return (
    <ScrollView style={styles.contentContainer}>
      <View style={styles.content}>
        {campData.map((item) => (
          <View key={item.docId} style={{ position: "relative" }}>
            <TouchableOpacity
              onPress={() => {
                console.log("Navigating to camp docId:", item.docId);
                Alert.alert("Navigating to camp", item.docId);
                router.push(`/education/${item.docId}`);
              }}
              activeOpacity={0.8}
            >
              <View>
                <EducationCard item={item} onEdit={onEdit} />
                <TouchableOpacity
                  style={{
                    position: "absolute",
                    bottom: 10,
                    right: 10,
                    backgroundColor: "#D52B1E",
                    borderRadius: 16,
                    padding: 6,
                    zIndex: 2,
                    elevation: 3,
                    marginBottom: 16,
                    marginRight: 3,
                  }}
                  onPress={() => {
                    Alert.alert(
                      "Delete Camp",
                      "Are you sure you want to delete this camp?",
                      [
                        { text: "Cancel", style: "cancel" },
                        {
                          text: "Delete",
                          style: "destructive",
                          onPress: () => onRemove(item),
                        },
                      ]
                    );
                  }}
                >
                  <Ionicons name="trash" size={22} color="#fff" />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </View>
        ))}
      </View>
    </ScrollView>
  );
}

function ShowcasesContent({
  educationData,
  onEdit,
}: {
  educationData: EducationItem[];
  onEdit: (item: EducationItem) => void;
}) {
  const router = useRouter();
  const showcaseData = educationData.filter((item) => item.type === "showcase");

  return (
    <ScrollView style={styles.contentContainer}>
      <View style={styles.content}>
        {showcaseData.map((item) => (
          <EducationCard key={item.docId} item={item} onEdit={onEdit} />
        ))}
      </View>
    </ScrollView>
  );
}

export default function EducationScreen() {
  const [activeTab, setActiveTab] = useState("camps");
  const [showAddModal, setShowAddModal] = useState(false);
  const [educationData, setEducationData] = useState<EducationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingEducation, setEditingEducation] =
    useState<EducationDetails | null>(null);

  useEffect(() => {
    loadEducationData();
  }, []);

  const loadEducationData = async () => {
    try {
      setLoading(true);
      const data = await getEducationItems();
      setEducationData(data);
    } catch (error) {
      console.error("Error loading education data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterPress = () => {
    // Filter functionality can be implemented here
    console.log("Filter pressed in Education tab");
  };

  const handleAddPress = () => {
    setEditingEducation(null);
    setShowAddModal(true);
  };

  const handleEditPress = (item: EducationItem) => {
    setEditingEducation(item as EducationDetails);
    setShowAddModal(true);
  };

  const handleAddModalClose = () => {
    setShowAddModal(false);
    setEditingEducation(null);
    // Reload data after modal closes
    loadEducationData();
  };

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
  };

  const handleRemoveCamp = async (item: EducationItem) => {
    try {
      await deleteEducationItem(item.docId!); // Use docId, not id
      setEducationData((prev) => prev.filter((camp) => camp.docId !== item.docId));
    } catch (error) {
      Alert.alert("Error", "Failed to delete camp.");
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      );
    }

    switch (activeTab) {
      case "camps":
        return (
          <HockeyCampsContent
            educationData={educationData}
            onEdit={handleEditPress}
            onRemove={handleRemoveCamp}
          />
        );
      case "showcases":
        return (
          <ShowcasesContent
            educationData={educationData}
            onEdit={handleEditPress}
          />
        );
      default:
        return (
          <HockeyCampsContent
            educationData={educationData}
            onEdit={handleEditPress}
            onRemove={handleRemoveCamp} // <-- add this line
          />
        );
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Education"
        onFilterPress={handleFilterPress}
        onAddPress={handleAddPress}
      />
      <EducationTabs activeTab={activeTab} onTabPress={handleTabPress} />
      {renderContent()}

      <AddEducationModal
        visible={showAddModal}
        onClose={handleAddModalClose}
        editingEducation={editingEducation}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  contentContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  content: {
    paddingHorizontal: 10,
    paddingVertical: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  viewAllContainer: {
    alignItems: "center",
    marginTop: 16,
    marginBottom: 24,
  },
  viewAllButton: {
    backgroundColor: "#D52B1E",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 200,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  viewAllText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
