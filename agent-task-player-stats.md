# Agent Task: Refactor Tournament Player Statistics Page

## Objective
Refactor the tournament statistics page for players (located in the tournament tabs - statistics header tab) to:
- Use the design provided in the Figma-to-React Native code (to be pasted below).
- Prepare the codebase for easy Firebase integration for per-player statistics.
- Create a new player statistics page (similar to `[id]` pattern) that includes:
  - Player info (name, team, etc.)
  - Player statistics (goals, assists, etc.)
  - Player description
- Use mock data for now.

## Steps
1. Refactor the tournament statistics page for players using the provided Figma-to-React Native code.
2. Create a new player statistics page/component (patterned after `[id]`) for individual player stats.
3. Prepare the new page/component for Firebase integration (structure, mock data, etc.).
4. Use mock data for player info, statistics, and description.

---

## Figma-to-React Native Code for player stats list showcase of the players. 

The code is not provided for the player stats page itself. You just follow the design and layout principles in the project and make it yourself.

```
import * as React from "react";
import {Image, StyleSheet, Text, View} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const Frame89 = () => {
  	
  	return (
    		<SafeAreaView style={styles.parent}>
      			<View style={styles.view}>
        				<View style={styles.frameParentShadowBox}>
          					<View style={styles.frameGroup}>
            						<Image style={styles.frameChild} resizeMode="cover" source="Frame 87.png" />
            						<View style={styles.johnStewParent}>
              							<Text style={[styles.johnStew, styles.textClr]}>John Stew</Text>
              							<Text style={styles.center}>Center</Text>
            						</View>
          					</View>
          					<Text style={[styles.text, styles.textClr]}>19</Text>
        				</View>
        				<View style={styles.frameParentShadowBox}>
          					<View style={styles.frameGroup}>
            						<Image style={styles.frameChild} resizeMode="cover" source="Frame 87.png" />
            						<View style={styles.johnStewParent}>
              							<Text style={[styles.johnStew, styles.textClr]}>Andrei Mihalich</Text>
              							<Text style={styles.center}>Right Winger</Text>
            						</View>
          					</View>
          					<Text style={[styles.text, styles.textClr]}>59</Text>
        				</View>
        				<View style={styles.frameParentShadowBox}>
          					<View style={styles.frameGroup}>
            						<Image style={styles.frameChild} resizeMode="cover" source="Frame 87.png" />
            						<View style={styles.johnStewParent}>
              							<Text style={[styles.johnStew, styles.textClr]}>Li Steward</Text>
              							<Text style={styles.center}>Defender</Text>
            						</View>
          					</View>
          					<Text style={[styles.text, styles.textClr]}>01</Text>
        				</View>
        				<View style={styles.frameParentShadowBox}>
          					<View style={styles.frameGroup}>
            						<Image style={styles.frameChild} resizeMode="cover" source="Frame 87.png" />
            						<View style={styles.johnStewParent}>
              							<Text style={[styles.johnStew, styles.textClr]}>Solomon Kim</Text>
              							<Text style={styles.center}>Goalie</Text>
            						</View>
          					</View>
          					<Text style={[styles.text, styles.textClr]}>07</Text>
        				</View>
      			</View>
    		</SafeAreaView>);
};

const styles = StyleSheet.create({
  	parent: {
    		flex: 1
  	},
  	textClr: {
    		color: "#231716",
    		textAlign: "left"
  	},
  	frameChild: {
    		width: 60,
    		borderRadius: 4,
    		height: 60,
    		overflow: "hidden"
  	},
  	johnStew: {
    		fontSize: 18,
    		lineHeight: 19,
    		fontWeight: "700",
    		fontFamily: "Archivo-Bold",
    		textAlign: "left"
  	},
  	center: {
    		fontSize: 16,
    		lineHeight: 17,
    		fontFamily: "Archivo-Regular",
    		color: "#7f7271",
    		textAlign: "left"
  	},
  	johnStewParent: {
    		gap: 5,
    		justifyContent: "center"
  	},
  	frameGroup: {
    		gap: 12,
    		flexDirection: "row",
    		justifyContent: "center",
    		alignItems: "center"
  	},
  	text: {
    		fontSize: 36,
    		lineHeight: 38,
    		fontFamily: "RacingSansOne-Regular",
    		textAlign: "left"
  	},
  	frameParentShadowBox: {
    		gap: 0,
    		padding: 10,
    		justifyContent: "space-between",
    		backgroundColor: "#fff",
    		borderRadius: 5,
    		shadowOpacity: 1,
    		elevation: 8,
    		shadowRadius: 8,
    		shadowOffset: {
      			width: 0,
      			height: 0
    		},
    		shadowColor: "rgba(0, 0, 0, 0.05)",
    		alignSelf: "stretch",
    		flexDirection: "row",
    		overflow: "hidden",
    		alignItems: "center"
  	},
  	view: {
    		width: "100%",
    		gap: 10,
    		justifyContent: "center",
    		alignItems: "center",
    		flex: 1
  	}
});

export default Frame89;
```
---

## Acceptance Criteria
- Tournament player statistics page is refactored to match the provided design.
- New player statistics page/component exists and is ready for Firebase integration.
- Player info, statistics, and description are displayed using mock data.
- Code is clean, modular, and ready for further Firebase work.
- Use design and layout that is used in the project and similar cases for consistency.
