/**
 * Import function triggers from their respective submodules:
 *
 * const {onCall} = require("firebase-functions/v2/https");
 * const {onDocumentWritten} = require("firebase-functions/v2/firestore");
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

const functions = require("firebase-functions");
const admin = require("firebase-admin");
admin.initializeApp();

// Create and deploy your first functions
// https://firebase.google.com/docs/functions/get-started

// exports.helloWorld = onRequest((request, response) => {
//   logger.info("Hello logs!", {structuredData: true});
//   response.send("Hello from Firebase!");
// });

// This function will run on every create/update in the "education" collection.

exports.syncCampIdField = functions.firestore
    .document("education/{docId}")
    .onWrite(async (change, context) => {
      const docId = context.params.docId;
      const docRef = change.after.ref;
      const data = change.after.exists ? change.after.data() : null;

      // Only update if type is camp and id is missing or incorrect
      if (data && data.type === "camp" && data.id !== docId) {
        await docRef.update({id: docId});
      }
      return null;
    });
