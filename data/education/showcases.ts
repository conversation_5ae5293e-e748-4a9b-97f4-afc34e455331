import { getEducationItems, type EducationItem } from './education';

export interface Showcase {
  id: string;
  name: string;
  duration: string;
  date: string;
  location: string;
  ageGroup: string;
  participants: number;
  maxParticipants: number;
  status: "Open" | "Almost Full" | "Full" | "Completed" | "Cancelled";
  price: string;
  image: string;
  organizer: string;
  description: string;
  skills: string[];
  scouts: string[];
  type: "upcoming" | "registered" | "completed";
  registrationDeadline: string;
  level: "Recreational" | "Competitive" | "Elite";
}

// Convert EducationItem to Showcase format
const educationItemToShowcase = (item: EducationItem): Showcase => {
  // Parse the subtitle to extract information
  const subtitleParts = item.subtitle.split(' • ');
  const duration = subtitleParts[0] || '1 Day';
  const ageGroup = subtitleParts[1] || 'All Ages';
  const location = subtitleParts[2] || 'TBD';

  return {
    id: item.docId,
    name: item.title,
    duration,
    date: item.date,
    location,
    ageGroup,
    participants: Math.floor(Math.random() * 15) + 3,
    maxParticipants: 20,
    status: "Open",
    price: "$150",
    image: item.image,
    organizer: "Hockey Development Association",
    description: `Join us for ${item.title}. This showcase event is designed for ${ageGroup}.`,
    skills: item.tags.filter(tag => !["Open", "Almost Full", "Full", "Completed", "Recreational", "Competitive", "Elite"].includes(tag)),
    scouts: ["Junior League Scouts", "College Recruiters"],
    type: "upcoming",
    registrationDeadline: new Date(new Date(item.date).getTime() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    level: item.tags.find(tag => ["Recreational", "Competitive", "Elite"].includes(tag)) as any || "Competitive",
  };
};

export const showcases: Showcase[] = [];

export const getAllShowcases = async (): Promise<Showcase[]> => {
  try {
    const educationItems = await getEducationItems();
    const showcaseItems = educationItems.filter(item => item.type === 'showcase');
    return showcaseItems.map(educationItemToShowcase);
  } catch (error) {
    console.error('Error getting showcases from education items:', error);
    return [];
  }
};

export const getShowcaseById = async (id: string): Promise<Showcase | undefined> => {
  const allShowcases = await getAllShowcases();
  return allShowcases.find((showcase) => showcase.id === id);
};

export const getShowcasesByType = async (type: "upcoming" | "registered" | "completed"): Promise<Showcase[]> => {
  const allShowcases = await getAllShowcases();
  return allShowcases.filter((showcase) => showcase.type === type);
};

export const getUpcomingShowcases = async (): Promise<Showcase[]> => {
  const allShowcases = await getAllShowcases();
  return allShowcases.filter((showcase) => showcase.type === "upcoming");
};

export const getRegisteredShowcases = async (): Promise<Showcase[]> => {
  const allShowcases = await getAllShowcases();
  return allShowcases.filter((showcase) => showcase.type === "registered");
};

export const getCompletedShowcases = async (): Promise<Showcase[]> => {
  const allShowcases = await getAllShowcases();
  return allShowcases.filter((showcase) => showcase.type === "completed");
};
