import { getEducationItems, type EducationItem } from './education';
import { getFirestore, doc, getDoc } from "firebase/firestore";
import { initializeApp, getApps, getApp } from "firebase/app";

// ...your firebaseConfig and db initialization...

export interface Camp {
  id: string;
  name: string;
  duration: string;
  startDate: string;
  endDate: string;
  ageGroup: string;
  participants: number;
  maxParticipants: number;
  status: "Open" | "Almost Full" | "Full" | "Completed" | "Cancelled";
  price: string;
  location: string;
  image: string;
  coach: string;
  coachImage: string;
  description: string;
  skills: string[];
  equipment: string[];
  type: "upcoming" | "camp" | "completed";
  registrationDeadline: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced" | "All Levels";
}

// Convert EducationItem to Camp format
const educationItemToCamp = (item: EducationItem): Camp => {
  const subtitleParts = item.subtitle.split(' • ');
  const duration = subtitleParts[0] || '1 Day';
  const ageGroup = subtitleParts[1] || 'All Ages';
  const location = subtitleParts[2] || 'TBD';

  // Safe date parsing for registrationDeadline
  let registrationDeadline = "";
  const parsedDate = new Date(item.date);
  if (!isNaN(parsedDate.getTime())) {
    registrationDeadline = new Date(parsedDate.getTime() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split('T')[0];
  } else {
    registrationDeadline = ""; // or use a default/fallback value
  }

  return {
    id: item.docId,
    name: item.title,
    duration,
    startDate: item.date,
    endDate: item.date,
    ageGroup,
    participants: Math.floor(Math.random() * 20) + 5,
    maxParticipants: 30,
    status: "Open",
    price: "$200",
    location,
    image: item.image,
    coach: "Mike Johnson",
    coachImage: "https://picsum.photos/200/200?random=10",
    description: `Join us for ${item.title}. This comprehensive training program is designed for ${ageGroup}.`,
    skills: item.tags.filter(tag => !["Open", "Almost Full", "Full", "Completed", "Beginner", "Intermediate", "Advanced", "All Levels"].includes(tag)),
    equipment: ["Skates", "Helmet", "Gloves", "Stick"],
    type: item.type as Camp["type"],
    registrationDeadline,
    difficulty: item.tags.find(tag => ["Beginner", "Intermediate", "Advanced", "All Levels"].includes(tag)) as any || "All Levels",
  };
};

export const camps: Camp[] = [];

export const getCampById = async (docId: string): Promise<Camp | undefined> => {
  const db = getFirestore();
  console.log("getCampById called with docId:", docId); // Log the input docId
  const docRef = doc(db, "education", docId);
  const docSnap = await getDoc(docRef);
  console.log("docSnap.exists:", docSnap.exists()); // Log if document exists
  if (docSnap.exists()) {
    const item = docSnap.data();
    console.log("Fetched camp Firestore data:", item); // Log the raw Firestore data
    const campObj = {
      docId,
      title: item.title,
      subtitle: item.subtitle,
      date: item.date,
      image: item.image,
      tags: item.tags || [],
      type: item.type,
    };
    console.log("Converted EducationItem for camp:", campObj); // Log the converted object
    const camp = educationItemToCamp(campObj);
    console.log("Final Camp object:", camp); // Log the final Camp object
    return camp;
  } else {
    console.warn("No camp found for docId:", docId);
  }
  return undefined;
};

export const getAllCamps = async (): Promise<Camp[]> => {
  try {
    const educationItems = await getEducationItems();
    console.log("Fetched education items:", educationItems); // Log all education items
    const campItems = educationItems.filter(item => item.type === 'camp');
    console.log("Filtered camp items:", campItems); // Log filtered camp items
    const camps = campItems.map(educationItemToCamp);
    console.log("Mapped Camp objects:", camps); // Log final camp objects
    return camps;
  } catch (error) {
    console.error('Error getting camps from education items:', error);
    return [];
  }
};
